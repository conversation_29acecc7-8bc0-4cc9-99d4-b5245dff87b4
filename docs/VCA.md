### **最终详细设计：Visual Critic Agent (VCA) 与视觉反馈外循环**

#### **1. 核心定位与目标**

`Visual Critic Agent (VCA)` 是AI Agent系统中的**“艺术总监”**。它通过模拟人类设计师的审阅过程，实现**从被动生成到主动迭代**的智能飞跃。

*   **主要目标:**
    1.  **自动化视觉审计:** 量化评估生成的3D模型渲染图与用户原始意图（图像/文本）之间的一致性。
    2.  **生成可执行的修正案:** 将视觉差异转化为结构化的、可被其他Agent理解和执行的修改指令。
    3.  **驱动迭代优化:** 作为视觉反馈外循环的核心，引导整个系统逐步逼近最终理想效果。

#### **2. 架构与模块设计**

VCA本身是一个独立的、由多模态LLM（MLLM）驱动的智能体。

```mermaid
graph TD
    subgraph VisualCriticAgent
        direction LR
        Input[Input Interface] --> Preproc[Image Preprocessing]
        Preproc --> Analyzer[Core Analysis Engine (MLLM)]
        Analyzer --> FeedbackGen[Feedback Generator]
        FeedbackGen --> Output[Output Interface]
    end
```

*   **2.1. Input Interface:**
    *   **职责:** 接收任务所需的所有上下文。
    *   **输入数据结构 (Pydantic Model):**
        ```python
        class VCARequest(BaseModel):
            source_image_path: str
            rendered_image_path: str
            current_spec: Dict[str, Any] # The JSON spec that generated the model
            initial_prompt: Optional[str] = None # User's initial text prompt
            iteration_count: int
        ```

*   **2.2. Image Preprocessing:**
    *   **职责:** 确保输入MLLM的图像数据是标准化的。
    *   **步骤:**
        1.  使用 `Pillow` 加载图像。
        2.  处理图像损坏、格式不兼容等异常。
        3.  将图像转换为Base64编码的字符串，以便嵌入到API请求中。
        4.  (可选) 统一调整尺寸到MLLM的最佳输入分辨率（如 512x512）。

*   **2.3. Core Analysis Engine (MLLM):**
    *   **职责:** 执行核心的视觉比较与分析。
    *   **核心技术:** 精心设计的**Prompt Engineering**。
    *   **Prompt模板 (最终版):**
        ```prompt
        # IDENTITY
        You are "VCA-1", a meticulous 3D Art Director AI. You are analytical, precise, and your goal is to guide a junior 3D artist (another AI) to perfection.

        # CONTEXT
        - Source of Truth: The user provided the "SOURCE IMAGE" and an initial request: "{initial_prompt}".
        - Current Attempt: We are at iteration {iteration_count}. The junior AI produced the "RENDERED IMAGE" based on this specification:
        ```json
        {current_spec}
        ```

        # PRIMARY DIRECTIVE
        Compare the RENDERED IMAGE to the SOURCE IMAGE. Your entire output MUST be a single, valid JSON object. Do not include any text outside of this JSON object.

        # JSON OUTPUT SCHEMA
        The JSON object must conform to the following structure:
        {
          "overall_consistency_score": "A float between 0.0 and 1.0, representing the overall match.",
          "is_acceptable": "A boolean. True if the score is > 0.95 or if no further improvements are obvious.",
          "analysis_summary": "A brief, one-sentence summary of your findings.",
          "detailed_critique": [
            {
              "aspect": "One of ['Geometry', 'Proportion', 'Color', 'Material', 'Style', 'Completeness']",
              "assessment": "A short, descriptive assessment of this aspect.",
              "confidence_score": "A float between 0.0 and 1.0 on how certain you are about this assessment."
            }
          ],
          "modification_suggestions": [
            {
              "target_component": "The exact component name from the provided specification JSON (e.g., 'Tabletop').",
              "parameter_to_change": "The JSON path to the parameter to be modified (e.g., 'dimensions.z', 'material.properties.base_color'). Use dot notation.",
              "suggested_change_instruction": "A clear, unambiguous instruction for the change. Be specific. Use phrases like 'Decrease value by 20%', 'Change hex value to #XXXXXX', 'Set value to realistic_wood', 'Apply a bevel modifier with 3 segments and 0.02m radius'.",
              "urgency": "An integer from 1 (minor tweak) to 5 (critical flaw)."
            }
          ]
        }

        # INSTRUCTIONS
        1. Analyze the images meticulously.
        2. Pay close attention to the provided JSON spec to identify correct component names and parameter paths.
        3. If the rendering is a good match, `is_acceptable` should be true and `modification_suggestions` can be an empty list.
        4. Prioritize suggestions by urgency.

        Now, perform your analysis and provide the JSON output.
        ```

*   **2.4. Feedback Generator:**
    *   **职责:** 解析和验证来自MLLM的响应。
    *   **步骤:**
        1.  接收MLLM的文本响应。
        2.  解析JSON字符串。如果失败，触发错误处理（重试或报告失败）。
        3.  使用Pydantic模型验证解析后的JSON对象是否符合预定义的Schema。
        4.  如果验证通过，将结构化的反馈对象传递给输出接口。

*   **2.5. Output Interface:**
    *   **职责:** 将经过验证的结构化反馈传递给`OrchestratorAgent`。
    *   **输出数据结构 (Pydantic Model):**
        ```python
        class VCAResponse(BaseModel):
            overall_consistency_score: float
            is_acceptable: bool
            analysis_summary: str
            detailed_critique: List[Dict]
            modification_suggestions: List[Dict]
        ```

#### **3. 视觉反馈外循环 (Outer Loop) 集成流程**

该循环由`OrchestratorAgent`精确调度，是系统自我完善的核心。

1.  **内循环完成:** `CodeGenerationAgent` -> `BlenderExecutor` -> **成功**。
2.  **渲染触发:** `BlenderExecutor`调用一个`RenderFarm`模块，该模块负责在一致的灯光和相机设置下，渲染出`preview.png`。
3.  **VCA激活:** `OrchestratorAgent`检测到渲染成功，创建一个`VCARequest`对象，并调用`VCA.critique(request)`。
4.  **VCA执行:** VCA内部完成其所有模块流程，返回一个`VCAResponse`对象。
5.  **决策分流 (Orchestration Logic):** `OrchestratorAgent`执行以下逻辑：
    ```python
    vca_response = vca.critique(request)

    if vca_response.is_acceptable or request.iteration_count >= MAX_ITERATIONS:
        # 任务成功结束
        finalize_task(status="Success", final_model=...)
    else:
        # 需要迭代，激活修正流程
        # 按urgency对建议进行排序
        sorted_suggestions = sorted(vca_response.modification_suggestions, key=lambda x: x['urgency'], reverse=True)

        # 调用SpecRefinementAgent
        new_spec = spec_refiner.refine(
            original_spec=request.current_spec,
            suggestions=sorted_suggestions
        )

        # 准备下一次内循环
        start_new_inner_loop(spec=new_spec, iteration_count=request.iteration_count + 1)
    ```
6.  **`SpecRefinementAgent`的角色:** 这是一个小而美的Agent，其核心是一个LLM，任务是将`suggested_change_instruction`（自然语言）应用到JSON规格文件上。它接收旧规格和建议列表，输出新规格。
7.  **循环继续:** 新的规格文件被送回内循环的起点，重复整个过程。

#### **4. 量化标准与验收条件 (Quantifiable Acceptance Criteria)**

*   **功能性:**
    *   **AC1:** VCA能成功处理99%的有效输入请求而不抛出未捕获的异常。
    *   **AC2:** MLLM返回的JSON能被成功解析和验证的比例 > 95%。
*   **质量:**
    *   **AC3:** 在一个包含20个多样化样本的基准测试集上，VCA的`overall_consistency_score`与人类专家的评分（1-5分制转换）相关性 > 0.7。
    *   **AC4:** 对于评估为不满意的样本，`modification_suggestions`中至少有1条被人类专家评为“高度相关且可操作”的比例 > 80%。
    *   **AC5:** 在端到端测试中，启用VCA外循环后，达到“高质量”模型所需的总迭代次数平均减少30%以上。
*   **性能:**
    *   **AC6:** VCA从接收请求到返回响应的P95延迟 < 10秒（使用云端API）。

#### **5. 风险管理**

*   **风险1: MLLM幻觉/输出格式错误。**
    *   **缓解:**
        1.  **强Schema和验证:** 使用Pydantic在接收端进行严格验证。
        2.  **重试与降级:** 对API调用失败或格式错误实现最多3次重试。如果仍然失败，记录错误并优雅地结束循环，标记为“需要人工干预”。
        3.  **Prompt优化:** 持续迭代Prompt，特别是`# JSON OUTPUT SCHEMA`部分，使其尽可能清晰无歧义。
*   **风险2: 反馈质量低，导致无效迭代。**
    *   **缓解:**
        1.  **引入`confidence_score`:** 让MLLM自我评估其判断的置信度，低置信度的建议可以被忽略。
        2.  **引入`urgency`:** 优先处理最关键的缺陷。
        3.  **A/B测试:** 定期测试不同的MLLM模型或Prompt变体，选择效果最好的。
*   **风险3: 迭代震荡或无法收敛。**
    *   **缓解:**
        1.  **设置最大迭代次数:** 强制循环在一定次数后停止。
        2.  **收敛检测:** `OrchestratorAgent`监控`overall_consistency_score`的变化。如果连续2次迭代分数没有提升或下降，则判定为震荡，停止循环。

这份详细设计为VCA的开发和集成提供了清晰的蓝图，涵盖了从高层架构到具体实现细节的各个方面，并设定了可量化的成功标准，为项目的顺利推进奠定了坚实的基础。