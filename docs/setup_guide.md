# 项目环境设置指南

本指南将详细介绍如何使用 `conda` 管理项目依赖并设置开发环境。

## 1. 前提条件

在开始之前，请确保您的系统中已安装 `conda` (Anaconda 或 Miniconda)。如果尚未安装，请访问 [Miniconda官网](https://docs.conda.io/en/latest/miniconda.html) 或 [Anaconda官网](https://www.anaconda.com/products/distribution) 进行下载和安装。

当前项目环境使用的是 `/opt/data/dev/miniconda3/bin/conda` 路径下的 `conda`。

## 2. 环境设置步骤

本项目依赖于名为 `bl4.4env` 的 `conda` 环境。我们将激活此环境并安装所有必要的Python包。

1.  **激活 Conda 环境**:
    打开您的终端，导航到项目根目录（例如，包含 `models` 文件夹的目录），然后执行以下命令来激活 `bl4.4env` 环境：
    ```bash
    conda activate bl4.4env
    ```
    如果您当前的工作目录不是 `models` 目录，请先切换到 `models` 目录再执行后续步骤：
    ```bash
    cd models
    ```

2.  **安装项目依赖**:
    项目的所有Python依赖都列在 `requirements.txt` 文件中。在激活 `bl4.4env` 环境后，执行以下命令安装它们：
    ```bash
    pip install -r requirements.txt

    conda env export > environment.yml
    ```
    此命令将自动下载并安装 `requirements.txt` 中指定的所有包及其兼容版本。

## 3. 验证环境设置

安装完成后，您可以运行一个简单的命令来验证环境是否设置成功，例如检查 `pyautogen` 是否可以导入：

```bash
python -c "import autogen; print('AutoGen successfully imported!')"
```
如果没有报错并打印出“AutoGen successfully imported!”，则表示环境已成功配置。

## 4. 运行项目

环境设置完成后，您可以根据项目的具体启动指南（例如，运行主编排脚本或测试用例）来开始使用本项目。