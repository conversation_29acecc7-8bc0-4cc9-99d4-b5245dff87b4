# RL奖励函数设计文档

## 1. 概述

本文档详细描述了用于训练Agent决策策略的奖励函数设计，这些奖励函数用于指导Agent在Blender 3D建模任务中的工具选择和行动优化。

## 2. 奖励函数设计原则

### 2.1 核心原则
- **任务导向**: 奖励函数应直接反映任务完成的质量和效率
- **可解释性**: 奖励计算逻辑应清晰可理解，便于调试和优化
- **平衡性**: 避免奖励稀疏或过于密集，确保学习稳定性
- **可扩展性**: 设计应支持未来添加新的任务类型和工具

### 2.2 奖励组成结构
总奖励 = 基础奖励 + 效率奖励 + 质量奖励 + 序列奖励 - 惩罚项

## 3. 详细奖励函数定义

### 3.1 基础奖励 (Base Reward)
根据工具选择的适当性给予基础奖励。

#### 工具-任务适配矩阵
```
任务类型 \ 工具类型    | 图像分析 | 规格生成 | 代码生成 | Blender执行 | 验证输出 | 知识查询
图像分析任务          |   8.0   |   2.0   |   1.0   |    1.0     |   3.0   |   6.0
规格生成任务          |   4.0   |   9.0   |   2.0   |    1.0     |   3.0   |   7.0
代码生成任务          |   2.0   |   3.0   |   9.0   |    2.0     |   4.0   |   8.0
Blender执行任务       |   1.0   |   1.0   |   3.0   |   10.0     |   6.0   |   4.0
验证任务             |   2.0   |   2.0   |   2.0   |    3.0     |   9.0   |   5.0
```

#### 计算公式
```python
base_reward = effectiveness_matrix[current_task][selected_tool]
```

### 3.2 效率奖励 (Efficiency Reward)
鼓励Agent快速完成任务，避免不必要的步骤。

#### 计算规则
- 步数 ≤ 3: +2.0 (高效完成)
- 步数 4-6: +1.0 (正常效率)
- 步数 7-10: 0.0 (中等效率)
- 步数 > 10: -1.0 (低效率惩罚)

#### 计算公式
```python
def calculate_efficiency_reward(step_count):
    if step_count <= 3:
        return 2.0
    elif step_count <= 6:
        return 1.0
    elif step_count <= 10:
        return 0.0
    else:
        return -1.0
```

### 3.3 质量奖励 (Quality Reward)
基于任务执行结果的质量给予奖励。

#### 质量评估标准
- **成功执行**: +3.0
- **部分成功**: +1.5
- **执行失败**: -2.0

#### 质量指标
1. **代码生成质量**
   - 语法正确性: 0-2分
   - 逻辑完整性: 0-2分
   - API使用正确性: 0-2分

2. **规格生成质量**
   - Schema符合性: 0-2分
   - 完整性: 0-2分
   - 准确性: 0-2分

3. **图像分析质量**
   - 对象识别准确率: 0-2分
   - 置信度: 0-2分
   - 特征提取完整性: 0-2分

### 3.4 序列奖励 (Sequence Reward)
鼓励Agent按照最优工具使用序列执行任务。

#### 序列奖励规则
- 首次使用必需工具: +1.5
- 重复使用必需工具: +0.5
- 使用非必需但有用的工具: +0.2
- 使用无关工具: 0.0

#### 最优序列定义
```python
optimal_sequences = {
    TaskType.IMAGE_ANALYSIS: [
        ToolType.ANALYZE_IMAGE,
        ToolType.QUERY_KNOWLEDGE
    ],
    TaskType.SPEC_GENERATION: [
        ToolType.QUERY_KNOWLEDGE,
        ToolType.GENERATE_SPEC
    ],
    TaskType.CODE_GENERATION: [
        ToolType.QUERY_KNOWLEDGE,
        ToolType.GENERATE_CODE,
        ToolType.VALIDATE_OUTPUT
    ]
}
```

### 3.5 惩罚项 (Penalty Terms)

#### 错误惩罚
- 工具执行失败: -2.0
- 重复错误: -1.0 × 错误次数
- 超时: -3.0

#### 资源浪费惩罚
- 过度使用工具: -0.5 × 超出次数
- 选择明显不当的工具: -1.0

## 4. 奖励函数实现

### 4.1 总奖励计算
```python
def calculate_total_reward(self, tool, success, step_count, task_progress):
    # 基础奖励
    base_reward = self._calculate_tool_appropriateness(tool)
    
    # 效率奖励
    efficiency_reward = self._calculate_efficiency_modifier(step_count)
    
    # 序列奖励
    sequence_reward = self._calculate_sequence_bonus(tool)
    
    # 质量奖励
    quality_reward = 3.0 if success else -2.0
    
    # 进度奖励
    progress_reward = task_progress * 0.1
    
    total_reward = (base_reward + efficiency_reward + 
                   sequence_reward + quality_reward + progress_reward)
    
    return total_reward
```

### 4.2 动态奖励调整
根据Agent的学习进度动态调整奖励权重：

```python
def get_dynamic_weights(training_iteration):
    # 早期训练：重视基础奖励
    if training_iteration < 100:
        return {
            'base': 1.0,
            'efficiency': 0.5,
            'quality': 0.8,
            'sequence': 0.3
        }
    # 中期训练：平衡各项奖励
    elif training_iteration < 500:
        return {
            'base': 0.8,
            'efficiency': 0.8,
            'quality': 1.0,
            'sequence': 0.6
        }
    # 后期训练：重视效率和质量
    else:
        return {
            'base': 0.6,
            'efficiency': 1.0,
            'quality': 1.2,
            'sequence': 0.8
        }
```

## 5. 奖励函数验证

### 5.1 单元测试
- 测试各个奖励组件的计算正确性
- 验证边界条件处理
- 确保奖励范围合理

### 5.2 集成测试
- 测试完整奖励计算流程
- 验证不同场景下的奖励分布
- 检查奖励信号的一致性

### 5.3 性能指标
- **收敛速度**: 策略收敛所需的训练步数
- **最终性能**: 训练完成后的任务成功率
- **稳定性**: 奖励信号的方差和稳定性

## 6. 奖励函数调优

### 6.1 超参数调优
- 奖励权重系数
- 惩罚强度
- 奖励衰减因子

### 6.2 A/B测试
比较不同奖励函数设计的效果：
- 基线奖励函数 vs 改进版本
- 不同权重配置的比较
- 静态权重 vs 动态权重

### 6.3 人工评估
- 专家评估Agent行为的合理性
- 对比人工决策和Agent决策
- 收集用户反馈进行奖励函数优化

## 7. 未来扩展

### 7.1 多目标奖励
- 同时优化多个目标（速度、质量、资源使用）
- 使用帕累托最优方法

### 7.2 层次化奖励
- 高层任务奖励 + 低层动作奖励
- 长期奖励 + 短期奖励

### 7.3 自适应奖励
- 根据Agent能力自动调整奖励难度
- 基于历史表现动态修改奖励函数

## 8. 总结

本奖励函数设计充分考虑了Blender 3D建模任务的特点，通过多维度的奖励机制引导Agent学习最优的工具选择和执行策略。设计具有良好的可解释性和可扩展性，为后续的系统优化提供了坚实基础。
