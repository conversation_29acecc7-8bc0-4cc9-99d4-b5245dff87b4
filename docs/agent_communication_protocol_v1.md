# Agent Communication Protocol v1

This document defines the initial version of the communication protocol for agents in the system.

## Message Structure

All messages between agents will be in JSON format and will adhere to the following structure:

```json
{
  "message_id": "string (uuid)",
  "sender_id": "string",
  "receiver_id": "string",
  "message_type": "string",
  "timestamp": "string (ISO 8601)",
  "payload": {},
  "metadata": {}
}
```

### Field Descriptions

*   `message_id`: A unique identifier for the message (e.g., a UUID).
*   `sender_id`: The ID of the agent sending the message.
*   `receiver_id`: The ID of the agent intended to receive the message.
*   `message_type`: The type of message being sent (e.g., `image_analysis_result`, `spec_request`).
*   `timestamp`: The time the message was sent, in ISO 8601 format.
*   `payload`: An object containing the actual data of the message.
*   `metadata`: An object containing any additional metadata about the message.

## Example Message

Here is an example of a message from an `ImageAnalysisAgent` to a `SpecGenerationAgent`:

```json
{
  "message_id": "uuid-1234",
  "sender_id": "ImageAnalysisAgent",
  "receiver_id": "SpecGenerationAgent",
  "message_type": "image_analysis_result",
  "timestamp": "2024-05-21T10:00:00Z",
  "payload": {
    "source_image_path": "/path/to/image.png",
    "detected_objects": [
      {"label": "cube", "bbox": [10, 10, 50, 50]},
      {"label": "sphere", "bbox": [60, 60, 100, 100]}
    ]
  },
  "metadata": {
    "confidence_score": 0.95
  }
}
```
