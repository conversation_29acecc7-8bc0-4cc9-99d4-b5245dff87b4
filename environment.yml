name: bl4.4env
channels:
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - bzip2=1.0.8=h5eee18b_6
  - ca-certificates=2025.2.25=h06a4308_0
  - ld_impl_linux-64=2.40=h12ee557_0
  - libffi=3.4.4=h6a678d5_1
  - libgcc-ng=11.2.0=h1234567_1
  - libgomp=11.2.0=h1234567_1
  - libstdcxx-ng=11.2.0=h1234567_1
  - libuuid=1.41.5=h5eee18b_0
  - libxcb=1.17.0=h9b100fa_0
  - ncurses=6.4=h6a678d5_0
  - openssl=3.0.16=h5eee18b_0
  - pip=25.1=pyhc872135_2
  - pthread-stubs=0.3=h0ce48e5_1
  - python=3.11.11=he870216_0
  - readline=8.2=h5eee18b_0
  - setuptools=78.1.1=py311h06a4308_0
  - sqlite=3.45.3=h5eee18b_0
  - tk=8.6.14=h993c535_1
  - tzdata=2025b=h04d1e81_0
  - wheel=0.45.1=py311h06a4308_0
  - xorg-libx11=1.8.12=h9b100fa_1
  - xorg-libxau=1.0.12=h9b100fa_0
  - xorg-libxdmcp=1.1.5=h9b100fa_0
  - xorg-xorgproto=2024.1=h5eee18b_1
  - xz=5.6.4=h5eee18b_1
  - zlib=1.2.13=h5eee18b_1
  - pip:
      - annotated-types==0.7.0
      - anyio==4.9.0
      - asyncer==0.0.8
      - attrs==25.3.0
      - backoff==2.2.1
      - bcrypt==4.3.0
      - build==1.2.2.post1
      - cachetools==5.5.2
      - certifi==2025.6.15
      - charset-normalizer==3.4.2
      - chromadb==1.0.13
      - click==8.2.1
      - coloredlogs==15.0.1
      - diskcache==5.6.3
      - distro==1.9.0
      - docker==7.1.0
      - durationpy==0.10
      - filelock==3.18.0
      - flatbuffers==25.2.10
      - fsspec==2025.5.1
      - google-auth==2.40.3
      - googleapis-common-protos==1.70.0
      - grpcio==1.73.1
      - h11==0.16.0
      - hf-xet==1.1.5
      - httpcore==1.0.9
      - httptools==0.6.4
      - httpx==0.28.1
      - huggingface-hub==0.33.1
      - humanfriendly==10.0
      - idna==3.10
      - importlib-metadata==8.7.0
      - importlib-resources==6.5.2
      - jinja2==3.1.6
      - jiter==0.10.0
      - jsonschema==4.24.0
      - jsonschema-specifications==2025.4.1
      - kubernetes==33.1.0
      - markdown-it-py==3.0.0
      - markupsafe==3.0.2
      - mdurl==0.1.2
      - mmh3==5.1.0
      - mpmath==1.3.0
      - networkx==3.5
      - numpy==2.3.1
      - nvidia-cublas-cu12==12.6.4.1
      - nvidia-cuda-cupti-cu12==12.6.80
      - nvidia-cuda-nvrtc-cu12==12.6.77
      - nvidia-cuda-runtime-cu12==12.6.77
      - nvidia-cudnn-cu12==9.5.1.17
      - nvidia-cufft-cu12==11.3.0.4
      - nvidia-cufile-cu12==1.11.1.6
      - nvidia-curand-cu12==10.3.7.77
      - nvidia-cusolver-cu12==11.7.1.2
      - nvidia-cusparse-cu12==12.5.4.2
      - nvidia-cusparselt-cu12==0.6.3
      - nvidia-nccl-cu12==2.26.2
      - nvidia-nvjitlink-cu12==12.6.85
      - nvidia-nvtx-cu12==12.6.77
      - oauthlib==3.3.1
      - onnxruntime==1.22.0
      - openai==1.93.0
      - opencv-python==4.11.0.86
      - opentelemetry-api==1.34.1
      - opentelemetry-exporter-otlp-proto-common==1.34.1
      - opentelemetry-exporter-otlp-proto-grpc==1.34.1
      - opentelemetry-proto==1.34.1
      - opentelemetry-sdk==1.34.1
      - opentelemetry-semantic-conventions==0.55b1
      - orjson==3.10.18
      - overrides==7.7.0
      - packaging==25.0
      - pillow==11.2.1
      - posthog==6.0.0
      - protobuf==5.29.5
      - pyasn1==0.6.1
      - pyasn1-modules==0.4.2
      - pyautogen==0.9.0
      - pybase64==1.4.1
      - pydantic==2.11.7
      - pydantic-core==2.33.2
      - pygments==2.19.2
      - pypika==0.48.9
      - pyproject-hooks==1.2.0
      - python-dateutil==2.9.0.post0
      - python-dotenv==1.1.1
      - pyyaml==6.0.2
      - referencing==0.36.2
      - regex==2024.11.6
      - requests==2.32.4
      - requests-oauthlib==2.0.0
      - rich==14.0.0
      - rpds-py==0.25.1
      - rsa==4.9.1
      - shellingham==1.5.4
      - six==1.17.0
      - sniffio==1.3.1
      - sympy==1.14.0
      - tenacity==9.1.2
      - termcolor==3.1.0
      - tiktoken==0.9.0
      - tokenizers==0.21.2
      - torch==2.7.1
      - torchvision==0.22.1
      - tqdm==4.67.1
      - triton==3.3.1
      - typer==0.16.0
      - typing-extensions==4.14.0
      - typing-inspection==0.4.1
      - urllib3==2.5.0
      - uvicorn==0.35.0
      - uvloop==0.21.0
      - watchfiles==1.1.0
      - websocket-client==1.8.0
      - websockets==15.0.1
      - zipp==3.23.0
prefix: /opt/data/dev/miniconda3/envs/bl4.4env
