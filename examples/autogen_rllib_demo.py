"""
AutoGen + Ray RLlib Integration Demo

This script demonstrates the integration of AutoGen and Ray RLlib
for agent communication and reinforcement learning-driven tool selection.
"""

import os
import sys
import json
import time
import autogen
import ray
from typing import Dict, Any, List

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from agents.autogen_om_poc import (
    AgentCommunicationProtocol,
    RLEnhancedAgent,
    RLEnhancedAssistantAgent,
    print_hello_world,
    analyze_image_mock,
    generate_spec_mock,
    generate_code_mock
)
from rl_env.minimal_blender_task_env import MinimalBlenderTaskEnv, TaskType, ToolType

def run_demo():
    """Run the AutoGen + Ray RLlib integration demo."""
    
    print("=== AutoGen + Ray RLlib Integration Demo ===\n")
    
    # Initialize Ray if not already initialized
    if not ray.is_initialized():
        ray.init(ignore_reinit_error=True)
    
    # 1. Create enhanced assistant agent
    assistant = RLEnhancedAssistantAgent(
        name="rl_assistant",
        system_message="You are an AI assistant enhanced with reinforcement learning for optimal tool selection. You can analyze images, generate specifications, and create code.",
        llm_config={
            "config_list": autogen.config_list_from_json("OAI_CONFIG_LIST"),
        },
    )
    
    # 2. Create user proxy agent
    user_proxy = autogen.UserProxyAgent(
        name="user_proxy",
        is_termination_msg=lambda x: x.get("content", "").rstrip().endswith("TERMINATE"),
        human_input_mode="NEVER",
        max_consecutive_auto_reply=3,
        code_execution_config=False,
    )
    
    # 3. Define test scenarios
    test_scenarios = [
        "Use the print_hello_world tool to print 'RL Integration Test'",
        "Analyze an image of a 3D cube for modeling purposes",
        "Generate a specification for a simple geometric shape",
    ]
    
    # 4. Run test scenarios
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n--- Test Scenario {i} ---")
        print(f"Scenario: {scenario}")
        
        # Initiate chat
        user_proxy.initiate_chat(
            assistant,
            message=scenario,
        )
        
        # Display protocol messages
        if hasattr(assistant, 'message_history') and assistant.message_history:
            print("\nProtocol Messages:")
            for msg in assistant.message_history[-2:]:  # Show last 2 messages
                print(f"  {msg['message_type']}: {msg['payload']}")
        
        # Add a small delay between scenarios
        time.sleep(1)
    
    # 5. Demonstrate RL environment
    print("\n--- RL Environment Demo ---")
    env = MinimalBlenderTaskEnv()
    observation, _ = env.reset()
    
    print("Initial state:", observation)
    
    # Run a few steps
    for i in range(3):
        # Select action (in a real scenario, this would come from the trained policy)
        action = env.action_space.sample()
        tool_name = ToolType(action).name
        
        print(f"\nStep {i+1}: Selecting tool '{tool_name}'")
        observation, reward, terminated, truncated, info = env.step(action)
        
        print(f"Reward: {reward:.2f}")
        print(f"New state: {observation}")
        print(f"Info: {json.dumps(info, default=str)}")
        
        if terminated or truncated:
            print("Episode ended")
            break
    
    # 6. Demonstrate communication protocol
    print("\n--- Communication Protocol Demo ---")
    protocol = AgentCommunicationProtocol()
    
    # Create a sample message
    message = protocol.create_message(
        sender_id="ImageAnalysisAgent",
        receiver_id="SpecGenerationAgent",
        message_type="image_analysis_result",
        payload={
            "source_image_path": "/path/to/image.png",
            "detected_objects": [
                {"label": "cube", "bbox": [10, 10, 50, 50]},
                {"label": "sphere", "bbox": [60, 60, 100, 100]}
            ]
        },
        metadata={
            "confidence_score": 0.95
        }
    )
    
    print("Sample protocol message:")
    print(json.dumps(message, indent=2))
    
    # Validate the message
    is_valid = protocol.validate_message(message)
    print(f"Message is valid: {is_valid}")
    
    print("\n=== Demo Complete ===")

if __name__ == "__main__":
    try:
        run_demo()
    except Exception as e:
        print(f"Error during demo: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Cleanup Ray
        if ray.is_initialized():
            ray.shutdown()
