"""
Minimal Blender Task Environment for Ray RLlib Training

This module implements a simplified RL environment that simulates
agent interactions with Blender tasks, focusing on tool selection
and task execution optimization.
"""

import gymnasium as gym
import numpy as np
from typing import Dict, Any, List, Tuple
from ray.rllib.env.env_context import EnvContext
import json
from enum import Enum

class TaskType(Enum):
    """Enumeration of different task types in the Blender workflow."""
    IMAGE_ANALYSIS = 0
    SPEC_GENERATION = 1
    CODE_GENERATION = 2
    BLENDER_EXECUTION = 3
    VALIDATION = 4

class ToolType(Enum):
    """Enumeration of available tools for agents."""
    PRINT_HELLO = 0
    ANALYZE_IMAGE = 1
    GENERATE_SPEC = 2
    GENERATE_CODE = 3
    EXECUTE_BLENDER = 4
    VALIDATE_OUTPUT = 5
    QUERY_KNOWLEDGE = 6

class MinimalBlenderTaskEnv(gym.Env):
    """
    Minimal RL environment for Blender task execution.
    
    This environment simulates the decision-making process of agents
    in a Blender 3D modeling pipeline, where agents must select
    appropriate tools and actions to complete tasks efficiently.
    """
    
    def __init__(self, config: EnvContext = None):
        super().__init__()
        
        self.config = config or {}
        
        # Environment parameters
        self.max_steps = self.config.get("max_steps", 20)
        self.num_tools = len(ToolType)
        self.num_task_types = len(TaskType)
        
        # State space: [current_task_type, task_progress, tools_used_count, 
        #               success_rate, error_count, context_complexity]
        self.observation_space = gym.spaces.Box(
            low=0.0, 
            high=10.0, 
            shape=(6,), 
            dtype=np.float32
        )
        
        # Action space: select a tool to use
        self.action_space = gym.spaces.Discrete(self.num_tools)
        
        # Initialize environment state
        self.reset()
    
    def reset(self, seed=None, options=None):
        """Reset the environment to initial state."""
        super().reset(seed=seed)
        
        # Initialize state variables
        self.current_task = np.random.choice(list(TaskType))
        self.task_progress = 0.0
        self.tools_used_count = 0
        self.success_rate = 0.0
        self.error_count = 0
        self.context_complexity = np.random.uniform(1.0, 5.0)
        self.step_count = 0
        
        # Task-specific parameters
        self.task_requirements = self._generate_task_requirements()
        self.execution_history = []
        
        # Create initial observation
        observation = self._get_observation()
        
        return observation, {}
    
    def step(self, action: int) -> Tuple[np.ndarray, float, bool, bool, Dict[str, Any]]:
        """
        Execute one step in the environment.
        
        Args:
            action: Selected tool (index from ToolType enum)
            
        Returns:
            observation: New state observation
            reward: Reward for the action
            terminated: Whether episode is finished
            truncated: Whether episode was truncated
            info: Additional information
        """
        self.step_count += 1
        
        # Get selected tool
        selected_tool = ToolType(action)
        
        # Execute action and calculate reward
        reward, success = self._execute_action(selected_tool)
        
        # Update state based on action results
        self._update_state(selected_tool, success, reward)
        
        # Check if episode is done
        terminated = self._is_task_complete() or self.error_count >= 3
        truncated = self.step_count >= self.max_steps
        
        # Create info dictionary
        info = {
            "selected_tool": selected_tool.name,
            "task_type": self.current_task.name,
            "task_progress": self.task_progress,
            "success": success,
            "step_count": self.step_count,
            "execution_history": self.execution_history[-5:]  # Last 5 actions
        }
        
        observation = self._get_observation()
        
        return observation, reward, terminated, truncated, info
    
    def _generate_task_requirements(self) -> Dict[str, Any]:
        """Generate requirements for the current task."""
        requirements = {
            "required_tools": [],
            "optimal_sequence": [],
            "complexity": self.context_complexity
        }
        
        # Define task-specific tool requirements
        task_tool_mapping = {
            TaskType.IMAGE_ANALYSIS: [ToolType.ANALYZE_IMAGE, ToolType.QUERY_KNOWLEDGE],
            TaskType.SPEC_GENERATION: [ToolType.GENERATE_SPEC, ToolType.QUERY_KNOWLEDGE],
            TaskType.CODE_GENERATION: [ToolType.GENERATE_CODE, ToolType.QUERY_KNOWLEDGE],
            TaskType.BLENDER_EXECUTION: [ToolType.EXECUTE_BLENDER, ToolType.VALIDATE_OUTPUT],
            TaskType.VALIDATION: [ToolType.VALIDATE_OUTPUT]
        }
        
        requirements["required_tools"] = task_tool_mapping.get(self.current_task, [])
        requirements["optimal_sequence"] = requirements["required_tools"].copy()
        
        return requirements
    
    def _execute_action(self, tool: ToolType) -> Tuple[float, bool]:
        """
        Execute the selected tool and return reward and success status.
        
        Args:
            tool: Selected tool to execute
            
        Returns:
            reward: Calculated reward for the action
            success: Whether the action was successful
        """
        # Record action in history
        self.execution_history.append({
            "step": self.step_count,
            "tool": tool.name,
            "task": self.current_task.name
        })
        
        # Calculate base reward based on tool appropriateness
        base_reward = self._calculate_tool_appropriateness(tool)
        
        # Add efficiency bonus/penalty
        efficiency_modifier = self._calculate_efficiency_modifier()
        
        # Add sequence bonus if tool follows optimal sequence
        sequence_bonus = self._calculate_sequence_bonus(tool)
        
        # Calculate final reward
        reward = base_reward + efficiency_modifier + sequence_bonus
        
        # Determine success (simplified logic)
        success = reward > 0 and np.random.random() > 0.2  # 80% success rate for positive rewards
        
        # Apply penalty for errors
        if not success:
            reward -= 2.0
            self.error_count += 1
        
        return reward, success
    
    def _calculate_tool_appropriateness(self, tool: ToolType) -> float:
        """Calculate how appropriate the selected tool is for the current task."""
        # Tool effectiveness matrix (task_type -> tool -> effectiveness)
        effectiveness_matrix = {
            TaskType.IMAGE_ANALYSIS: {
                ToolType.ANALYZE_IMAGE: 8.0,
                ToolType.QUERY_KNOWLEDGE: 6.0,
                ToolType.PRINT_HELLO: 1.0,
                ToolType.GENERATE_SPEC: 2.0,
                ToolType.GENERATE_CODE: 1.0,
                ToolType.EXECUTE_BLENDER: 1.0,
                ToolType.VALIDATE_OUTPUT: 3.0
            },
            TaskType.SPEC_GENERATION: {
                ToolType.GENERATE_SPEC: 9.0,
                ToolType.QUERY_KNOWLEDGE: 7.0,
                ToolType.ANALYZE_IMAGE: 4.0,
                ToolType.PRINT_HELLO: 1.0,
                ToolType.GENERATE_CODE: 2.0,
                ToolType.EXECUTE_BLENDER: 1.0,
                ToolType.VALIDATE_OUTPUT: 3.0
            },
            TaskType.CODE_GENERATION: {
                ToolType.GENERATE_CODE: 9.0,
                ToolType.QUERY_KNOWLEDGE: 8.0,
                ToolType.GENERATE_SPEC: 3.0,
                ToolType.ANALYZE_IMAGE: 2.0,
                ToolType.PRINT_HELLO: 1.0,
                ToolType.EXECUTE_BLENDER: 2.0,
                ToolType.VALIDATE_OUTPUT: 4.0
            },
            TaskType.BLENDER_EXECUTION: {
                ToolType.EXECUTE_BLENDER: 10.0,
                ToolType.VALIDATE_OUTPUT: 6.0,
                ToolType.GENERATE_CODE: 3.0,
                ToolType.QUERY_KNOWLEDGE: 4.0,
                ToolType.PRINT_HELLO: 1.0,
                ToolType.ANALYZE_IMAGE: 1.0,
                ToolType.GENERATE_SPEC: 1.0
            },
            TaskType.VALIDATION: {
                ToolType.VALIDATE_OUTPUT: 9.0,
                ToolType.QUERY_KNOWLEDGE: 5.0,
                ToolType.EXECUTE_BLENDER: 3.0,
                ToolType.PRINT_HELLO: 1.0,
                ToolType.ANALYZE_IMAGE: 2.0,
                ToolType.GENERATE_SPEC: 2.0,
                ToolType.GENERATE_CODE: 2.0
            }
        }
        
        return effectiveness_matrix.get(self.current_task, {}).get(tool, 1.0)
    
    def _calculate_efficiency_modifier(self) -> float:
        """Calculate efficiency bonus/penalty based on step count."""
        # Reward faster completion
        if self.step_count <= 3:
            return 2.0
        elif self.step_count <= 6:
            return 1.0
        elif self.step_count <= 10:
            return 0.0
        else:
            return -1.0
    
    def _calculate_sequence_bonus(self, tool: ToolType) -> float:
        """Calculate bonus for following optimal tool sequence."""
        required_tools = self.task_requirements.get("required_tools", [])
        
        if tool in required_tools:
            # Check if this tool hasn't been used yet
            used_tools = [ToolType[action["tool"]] for action in self.execution_history[:-1]]
            if tool not in used_tools:
                return 1.5  # Bonus for using required tool for first time
            else:
                return 0.5  # Smaller bonus for reusing required tool
        
        return 0.0
    
    def _update_state(self, tool: ToolType, success: bool, reward: float):
        """Update environment state based on action results."""
        self.tools_used_count += 1
        
        if success:
            self.task_progress = min(10.0, self.task_progress + reward / 5.0)
            # Update success rate (exponential moving average)
            self.success_rate = 0.8 * self.success_rate + 0.2 * 1.0
        else:
            # Update success rate
            self.success_rate = 0.8 * self.success_rate + 0.2 * 0.0
    
    def _is_task_complete(self) -> bool:
        """Check if the current task is complete."""
        return self.task_progress >= 8.0  # Task complete threshold
    
    def _get_observation(self) -> np.ndarray:
        """Get current state observation."""
        return np.array([
            float(self.current_task.value),  # current_task_type
            self.task_progress,              # task_progress
            min(10.0, self.tools_used_count), # tools_used_count (capped)
            self.success_rate,               # success_rate
            min(10.0, self.error_count),     # error_count (capped)
            self.context_complexity          # context_complexity
        ], dtype=np.float32)
    
    def render(self, mode="human"):
        """Render the environment state (optional)."""
        if mode == "human":
            print(f"Task: {self.current_task.name}")
            print(f"Progress: {self.task_progress:.2f}/10.0")
            print(f"Tools used: {self.tools_used_count}")
            print(f"Success rate: {self.success_rate:.2f}")
            print(f"Errors: {self.error_count}")
            print(f"Step: {self.step_count}/{self.max_steps}")
            print("---")
