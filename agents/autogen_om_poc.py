import autogen
import json
import uuid
from datetime import datetime
from typing import Dict, Any, List

# Try to import optional dependencies
try:
    import gymnasium as gym
    import numpy as np
    HAS_GYM = True
except ImportError:
    HAS_GYM = False
    print("Warning: gymnasium and numpy not available. RL features will be limited.")

try:
    from ray.rllib.algorithms.ppo import PPOConfig
    from ray.rllib.env.env_context import EnvContext
    import ray
    HAS_RAY = True

    # Initialize Ray
    if not ray.is_initialized():
        ray.init(ignore_reinit_error=True)
except ImportError:
    HAS_RAY = False
    print("Warning: Ray RLlib not available. Using mock RL implementation.")

class AgentCommunicationProtocol:
    """Handles agent communication according to the v1 protocol."""

    @staticmethod
    def create_message(sender_id: str, receiver_id: str, message_type: str,
                      payload: Dict[Any, Any], metadata: Dict[Any, Any] = None) -> Dict[str, Any]:
        """Create a message following the communication protocol v1."""
        return {
            "message_id": str(uuid.uuid4()),
            "sender_id": sender_id,
            "receiver_id": receiver_id,
            "message_type": message_type,
            "timestamp": datetime.now().isoformat(),
            "payload": payload,
            "metadata": metadata or {}
        }

    @staticmethod
    def validate_message(message: Dict[str, Any]) -> bool:
        """Validate message structure according to protocol v1."""
        required_fields = ["message_id", "sender_id", "receiver_id",
                          "message_type", "timestamp", "payload", "metadata"]
        return all(field in message for field in required_fields)

class SimpleToolSelectionEnv:
    """
    Simple RL environment for agent tool selection.

    State: Current task context and available tools
    Action: Select a tool from available options
    Reward: Based on tool selection effectiveness
    """

    def __init__(self, config=None):
        # Define available tools
        self.available_tools = [
            "print_hello_world",
            "analyze_image",
            "generate_spec",
            "generate_code"
        ]

        if HAS_GYM:
            # State space: [task_type, context_info, tool_history]
            # Simplified to discrete values for this POC
            self.observation_space = gym.spaces.Box(
                low=0, high=10, shape=(4,), dtype=np.float32
            )

            # Action space: select one of the available tools
            self.action_space = gym.spaces.Discrete(len(self.available_tools))
        else:
            # Mock spaces for when gym is not available
            self.observation_space = type('MockSpace', (), {'shape': (4,)})()
            self.action_space = type('MockSpace', (), {'n': len(self.available_tools)})()

        self.reset()

    def reset(self, seed=None, options=None):
        """Reset environment to initial state."""
        if HAS_GYM:
            # Initialize state: [task_type, context_complexity, tools_used, success_rate]
            self.state = np.array([1.0, 2.0, 0.0, 0.0], dtype=np.float32)
        else:
            # Mock state when numpy is not available
            self.state = [1.0, 2.0, 0.0, 0.0]

        self.step_count = 0
        self.max_steps = 10

        return self.state, {}

    def step(self, action):
        """Execute action and return new state, reward, done, info."""
        self.step_count += 1

        # Get selected tool
        selected_tool = self.available_tools[action]

        # Calculate reward based on tool appropriateness
        reward = self._calculate_reward(action)

        # Update state
        if HAS_GYM:
            self.state[2] += 1  # increment tools_used
            self.state[3] = reward / 10.0  # update success_rate
        else:
            self.state[2] += 1
            self.state[3] = reward / 10.0

        # Check if episode is done
        done = self.step_count >= self.max_steps or reward >= 10
        truncated = False

        info = {
            "selected_tool": selected_tool,
            "step_count": self.step_count
        }

        return self.state, reward, done, truncated, info

    def _calculate_reward(self, action):
        """Calculate reward based on tool selection."""
        # Simple reward function for POC
        # In real implementation, this would be based on actual task success
        tool_effectiveness = {
            0: 5,   # print_hello_world - basic tool
            1: 8,   # analyze_image - good for image tasks
            2: 7,   # generate_spec - good for specification tasks
            3: 9    # generate_code - high value tool
        }

        base_reward = tool_effectiveness.get(action, 1)

        # Add efficiency bonus for quick selection
        efficiency_bonus = max(0, 5 - self.step_count)

        return base_reward + efficiency_bonus

class MockRLAlgorithm:
    """Mock RL algorithm for when Ray is not available."""

    def __init__(self):
        self.training_iteration = 0

    def train(self):
        self.training_iteration += 1
        return {'episode_reward_mean': 5.0 + self.training_iteration * 0.5}

    def compute_single_action(self, state):
        # Simple heuristic: select tool based on state
        if isinstance(state, list):
            task_type = state[0]
        else:
            task_type = state[0] if hasattr(state, '__getitem__') else 1.0

        # Simple mapping: task_type to preferred tool
        if task_type <= 1.5:
            return 1  # analyze_image
        elif task_type <= 2.5:
            return 2  # generate_spec
        else:
            return 3  # generate_code

class RLEnhancedAgent:
    """Agent enhanced with RL-driven tool selection."""

    def __init__(self, agent_id: str):
        self.agent_id = agent_id
        self.communication = AgentCommunicationProtocol()

        # Initialize RL policy
        self.env = SimpleToolSelectionEnv()

        if HAS_RAY:
            # Configure PPO algorithm
            self.ppo_config = (
                PPOConfig()
                .environment(SimpleToolSelectionEnv)
                .framework("torch")
                .training(
                    lr=0.0001,
                    num_sgd_iter=10,
                    sgd_minibatch_size=64,
                    train_batch_size=1000,
                )
                .resources(num_gpus=0)
            )

            # Build the algorithm
            self.algorithm = self.ppo_config.build()

            # Train for a few iterations to have a basic policy
            for i in range(3):
                result = self.algorithm.train()
                print(f"Training iteration {i+1}: reward_mean = {result['episode_reward_mean']:.2f}")
        else:
            # Use mock algorithm when Ray is not available
            self.algorithm = MockRLAlgorithm()
            print("Using mock RL algorithm (Ray not available)")

            # Simulate training
            for i in range(3):
                result = self.algorithm.train()
                print(f"Mock training iteration {i+1}: reward_mean = {result['episode_reward_mean']:.2f}")

    def select_tool_with_rl(self, context: Dict[str, Any]) -> str:
        """Use RL policy to select the best tool for the given context."""
        # Get current state from context
        if HAS_GYM:
            state = np.array([1.0, 2.0, 0.0, 0.0], dtype=np.float32)
        else:
            state = [1.0, 2.0, 0.0, 0.0]

        # Get action from trained policy
        action = self.algorithm.compute_single_action(state)

        # Return selected tool
        return self.env.available_tools[action]

    def send_message(self, receiver_id: str, message_type: str, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Send a message using the communication protocol."""
        message = self.communication.create_message(
            sender_id=self.agent_id,
            receiver_id=receiver_id,
            message_type=message_type,
            payload=payload
        )

        print(f"Agent {self.agent_id} sending message: {json.dumps(message, indent=2)}")
        return message

# Enhanced tools with RL integration
def print_hello_world(message: str):
    """Prints a message to the console."""
    print(f"Hello, World! {message}")
    return "Message printed successfully."

def analyze_image_mock(image_path: str):
    """Mock image analysis tool."""
    return f"Analyzed image at {image_path}: detected cube and sphere objects."

def generate_spec_mock(analysis_result: str):
    """Mock specification generation tool."""
    return f"Generated 3D model spec based on: {analysis_result}"

def generate_code_mock(spec: str):
    """Mock code generation tool."""
    return f"Generated Blender Python code for spec: {spec}"

class RLEnhancedAssistantAgent(autogen.AssistantAgent):
    """AutoGen Assistant Agent enhanced with RL-driven tool selection."""

    def __init__(self, name: str, **kwargs):
        super().__init__(name=name, **kwargs)
        self.rl_agent = RLEnhancedAgent(name)
        self.message_history = []

        # Register all available tools
        self.register_function(
            function_map={
                "print_hello_world": print_hello_world,
                "analyze_image_mock": analyze_image_mock,
                "generate_spec_mock": generate_spec_mock,
                "generate_code_mock": generate_code_mock,
            }
        )

    def generate_reply(self, messages=None, sender=None, **kwargs):
        """Override to include RL-driven tool selection and protocol communication."""

        if messages:
            last_message = messages[-1] if isinstance(messages, list) else messages

            # Create protocol message for received communication
            protocol_message = self.rl_agent.send_message(
                receiver_id=sender.name if sender else "unknown",
                message_type="task_request",
                payload={"content": last_message.get("content", "")}
            )

            self.message_history.append(protocol_message)

            # Use RL to select appropriate tool based on message content
            context = {"message_content": last_message.get("content", "")}
            selected_tool = self.rl_agent.select_tool_with_rl(context)

            print(f"RL Agent selected tool: {selected_tool}")

            # Create response message using protocol
            response_message = self.rl_agent.send_message(
                receiver_id=sender.name if sender else "user_proxy",
                message_type="tool_selection_result",
                payload={
                    "selected_tool": selected_tool,
                    "context": context
                }
            )

            self.message_history.append(response_message)

        # Call parent's generate_reply method
        return super().generate_reply(messages, sender, **kwargs)

def run_enhanced_autogen_demo():
    """Run the enhanced AutoGen demo with RL integration."""

    print("=== Enhanced AutoGen + Ray RLlib Integration Demo ===\n")

    # Create enhanced assistant agent
    assistant = RLEnhancedAssistantAgent(
        name="rl_assistant",
        system_message="You are an AI assistant enhanced with reinforcement learning for optimal tool selection. You can analyze images, generate specifications, and create code.",
        llm_config={
            "config_list": autogen.config_list_from_json("OAI_CONFIG_LIST"),
        },
    )

    # Create user proxy agent
    user_proxy = autogen.UserProxyAgent(
        name="user_proxy",
        is_termination_msg=lambda x: x.get("content", "").rstrip().endswith("TERMINATE"),
        human_input_mode="NEVER",
        max_consecutive_auto_reply=3,
        code_execution_config=False,
    )

    # Test scenarios for RL tool selection
    test_scenarios = [
        "Use the print_hello_world tool to print 'RL Integration Test'",
        "Analyze an image of a 3D cube for modeling purposes",
        "Generate a specification for a simple geometric shape",
    ]

    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n--- Test Scenario {i} ---")
        print(f"Scenario: {scenario}")

        # Initiate chat
        user_proxy.initiate_chat(
            assistant,
            message=scenario,
        )

        print(f"Message history length: {len(assistant.message_history)}")

        # Display protocol messages
        if assistant.message_history:
            print("\nProtocol Messages:")
            for msg in assistant.message_history[-2:]:  # Show last 2 messages
                print(f"  {msg['message_type']}: {msg['payload']}")

    print("\n=== Demo Complete ===")
    print(f"Total protocol messages exchanged: {len(assistant.message_history)}")

    # Validate protocol compliance
    valid_messages = sum(1 for msg in assistant.message_history
                        if assistant.rl_agent.communication.validate_message(msg))
    print(f"Valid protocol messages: {valid_messages}/{len(assistant.message_history)}")

    return assistant.message_history

if __name__ == "__main__":
    try:
        message_history = run_enhanced_autogen_demo()
        print("\nSuccess: Enhanced AutoGen + Ray RLlib integration completed successfully!")
    except Exception as e:
        print(f"Error during demo: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Cleanup Ray
        if HAS_RAY and ray.is_initialized():
            ray.shutdown()
