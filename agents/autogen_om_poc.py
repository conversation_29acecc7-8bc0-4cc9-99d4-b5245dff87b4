import autogen
import json
import uuid
from datetime import datetime
from typing import Dict, Any, List
import gymnasium as gym
import numpy as np
from ray.rllib.algorithms.ppo import PPOConfig
from ray.rllib.env.env_context import EnvContext
import ray

# Initialize Ray
if not ray.is_initialized():
    ray.init(ignore_reinit_error=True)

class AgentCommunicationProtocol:
    """Handles agent communication according to the v1 protocol."""

    @staticmethod
    def create_message(sender_id: str, receiver_id: str, message_type: str,
                      payload: Dict[Any, Any], metadata: Dict[Any, Any] = None) -> Dict[str, Any]:
        """Create a message following the communication protocol v1."""
        return {
            "message_id": str(uuid.uuid4()),
            "sender_id": sender_id,
            "receiver_id": receiver_id,
            "message_type": message_type,
            "timestamp": datetime.now().isoformat(),
            "payload": payload,
            "metadata": metadata or {}
        }

    @staticmethod
    def validate_message(message: Dict[str, Any]) -> bool:
        """Validate message structure according to protocol v1."""
        required_fields = ["message_id", "sender_id", "receiver_id",
                          "message_type", "timestamp", "payload", "metadata"]
        return all(field in message for field in required_fields)

class SimpleToolSelectionEnv(gym.Env):
    """
    Simple RL environment for agent tool selection.

    State: Current task context and available tools
    Action: Select a tool from available options
    Reward: Based on tool selection effectiveness
    """

    def __init__(self, config: EnvContext = None):
        super().__init__()

        # Define available tools
        self.available_tools = [
            "print_hello_world",
            "analyze_image",
            "generate_spec",
            "generate_code"
        ]

        # State space: [task_type, context_info, tool_history]
        # Simplified to discrete values for this POC
        self.observation_space = gym.spaces.Box(
            low=0, high=10, shape=(4,), dtype=np.float32
        )

        # Action space: select one of the available tools
        self.action_space = gym.spaces.Discrete(len(self.available_tools))

        self.reset()

    def reset(self, seed=None, options=None):
        """Reset environment to initial state."""
        super().reset(seed=seed)

        # Initialize state: [task_type, context_complexity, tools_used, success_rate]
        self.state = np.array([1.0, 2.0, 0.0, 0.0], dtype=np.float32)
        self.step_count = 0
        self.max_steps = 10

        return self.state, {}

    def step(self, action):
        """Execute action and return new state, reward, done, info."""
        self.step_count += 1

        # Get selected tool
        selected_tool = self.available_tools[action]

        # Calculate reward based on tool appropriateness
        reward = self._calculate_reward(action)

        # Update state
        self.state[2] += 1  # increment tools_used
        self.state[3] = reward / 10.0  # update success_rate

        # Check if episode is done
        done = self.step_count >= self.max_steps or reward >= 10
        truncated = False

        info = {
            "selected_tool": selected_tool,
            "step_count": self.step_count
        }

        return self.state, reward, done, truncated, info

    def _calculate_reward(self, action):
        """Calculate reward based on tool selection."""
        # Simple reward function for POC
        # In real implementation, this would be based on actual task success
        tool_effectiveness = {
            0: 5,   # print_hello_world - basic tool
            1: 8,   # analyze_image - good for image tasks
            2: 7,   # generate_spec - good for specification tasks
            3: 9    # generate_code - high value tool
        }

        base_reward = tool_effectiveness.get(action, 1)

        # Add efficiency bonus for quick selection
        efficiency_bonus = max(0, 5 - self.step_count)

        return base_reward + efficiency_bonus

class RLEnhancedAgent:
    """Agent enhanced with RL-driven tool selection."""

    def __init__(self, agent_id: str):
        self.agent_id = agent_id
        self.communication = AgentCommunicationProtocol()

        # Initialize RL policy
        self.env = SimpleToolSelectionEnv()

        # Configure PPO algorithm
        self.ppo_config = (
            PPOConfig()
            .environment(SimpleToolSelectionEnv)
            .framework("torch")
            .training(
                lr=0.0001,
                num_sgd_iter=10,
                sgd_minibatch_size=64,
                train_batch_size=1000,
            )
            .resources(num_gpus=0)
        )

        # Build the algorithm
        self.algorithm = self.ppo_config.build()

        # Train for a few iterations to have a basic policy
        for i in range(3):
            result = self.algorithm.train()
            print(f"Training iteration {i+1}: reward_mean = {result['episode_reward_mean']:.2f}")

    def select_tool_with_rl(self, context: Dict[str, Any]) -> str:
        """Use RL policy to select the best tool for the given context."""
        # Get current state from context
        state = np.array([1.0, 2.0, 0.0, 0.0], dtype=np.float32)

        # Get action from trained policy
        action = self.algorithm.compute_single_action(state)

        # Return selected tool
        return self.env.available_tools[action]

    def send_message(self, receiver_id: str, message_type: str, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Send a message using the communication protocol."""
        message = self.communication.create_message(
            sender_id=self.agent_id,
            receiver_id=receiver_id,
            message_type=message_type,
            payload=payload
        )

        print(f"Agent {self.agent_id} sending message: {json.dumps(message, indent=2)}")
        return message
