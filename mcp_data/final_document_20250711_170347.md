# 项目架构设计：基于图像与规格的Blender 3D模型生成AI Agent

## 1. 完整需求提取

本项目旨在开发一个先进的AI Agent系统，能够根据输入的图像自动在Blender中生成复杂的3D模型。核心需求概括如下：

*   **用户与场景：** 主要面向3D艺术家、游戏开发者、科研人员。系统需解决现有3D建模中从图像到模型效率低下的痛点，支持快速原型、批量生成及高精度定制。用户期望模型具备高精度、复杂性和可编辑性，并可能需要与现有3D软件集成。
*   **核心功能模块：**
    *   **图像输入：** 支持PNG、JPG格式，来源包括本地上传、URL和API集成（DALL-E, Stable Diffusion等）。
    *   **图像分析：** 提取物体识别、场景理解、深度信息、材质纹理等3D相关特征，并要求达到高精度粒度。
    *   **3D模型规格生成：** 需定义清晰的JSON/YAML Schema，包含模型组件、属性、层级关系、材质、尺寸等，以完整表达模型需求。
    *   **规格到代码生成：** 支持Blender Python API和Molecular Nodes (MCP)插件功能，能够处理复杂模型（如动画、物理模拟）的生成需求。
    *   **Blender执行：** 支持无头（headless）或带UI模式运行Blender，输出`.blend`文件、渲染图像或视频，并具备完善的错误处理与反馈机制。
*   **数据模型与存储：**
    *   **数据类型：** 原始图像、图像分析结果、3D模型规格、Blender Python脚本、最终.blend文件和渲染图片。
    *   **存储方案：**
        *   **非结构化数据（图像、模型文件）：** 对象存储（如AWS S3, MinIO）。
        *   **结构化数据（规格、脚本）：** 文档型数据库（如MongoDB, PostgreSQL with JSONB）。
        *   **知识库（向量嵌入）：** 向量数据库（如ChromaDB, FAISS）。
    *   **性能与可靠性：** 需满足高读写速度、高并发量、数据一致性、持久性及灾备要求。具备数据备份、恢复和生命周期管理策略。
    *   **安全性：** 用户数据隔离、加密、访问控制及合规性。
    *   **版本控制：** 对3D模型规格和Blender脚本需支持版本追踪和回溯。
*   **技术栈与非功能性需求：**
    *   **AI Agent框架：** 采用AutoGen (多Agent编排) 和 OpenMenus-RL (强化学习驱动的工具选择和执行优化)。
    *   **LLMs：** 优先考虑OpenAI API (GPT系列)，也可评估本地开源LLM (如Llama 2) 用于代码生成和图像理解。
    *   **性能：** 图像处理、3D模型生成与渲染需具备快速响应时间、高吞吐量和并发处理能力。
    *   **可伸缩性：** 支持横向扩展，可利用GPU加速。
    *   **可用性、可靠性、可维护性：** 设定SLA目标，具备故障恢复机制，建立完善的日志和监控体系。
*   **UI/UX设计原则：**
    *   **设计风格：** 可选择Blender原生、Web界面或混合模式。
    *   **交互流程：** 确保图像输入、分析结果查看、规格调整、模型预览等关键环节操作顺畅直观。
    *   **透明性：** 清晰展示AI分析、规格生成、Blender执行各阶段进度和结果，提供实时反馈。
    *   **帮助与错误处理：** 提供有效的帮助信息、错误提示和操作指导。
    *   **个性化：** 允许用户自定义界面布局、偏好设置和保存常用配置。

## 2. 技术栈选型

综合以上需求，建议的技术栈如下：

*   **编程语言：** Python (核心逻辑、AI Agent、Blender集成)
*   **AI Agent框架：**
    *   **AutoGen：** 用于多Agent间的对话、协作和任务编排。
    *   **OpenMenus-RL：** 增强Agent的工具选择和行动优化能力。
*   **大型语言模型 (LLMs)：**
    *   **OpenAI API (GPT-4V, GPT-4, GPT-3.5)：** 图像理解（GPT-4V）、规格生成、代码生成。
    *   **Hugging Face Transformers / Local LLM (e.g., Llama 2)：** 可选，用于私有化部署或特定领域微调。
*   **计算机视觉：**
    *   **PyTorch / TensorFlow：** 深度学习框架。
    *   **预训练模型：** YOLO, DETR, Mask R-CNN (用于对象识别、语义分割)。
    *   **OpenCV：** 图像预处理、后处理。
*   **Blender集成：**
    *   `bpy` (Blender官方Python API)：控制Blender各项操作。
    *   **Molecular Nodes (MCP) 插件：** 用于复杂分子结构或程序化模型的生成。
*   **数据存储：**
    *   **对象存储：** MinIO (私有化部署) 或 AWS S3/Azure Blob Storage/Google Cloud Storage (云部署) - 存储原始图像、生成模型文件（.blend, 渲染图片）。
    *   **文档型数据库：** PostgreSQL (带JSONB字段) 或 MongoDB - 存储3D模型规格、Blender Python脚本、用户偏好设置。
    *   **向量数据库：** ChromaDB / FAISS / Milvus - 知识Agent的向量嵌入检索。
*   **API / Web框架：** FastAPI / Flask (提供RESTful API接口供前端或其他系统调用)。
*   **容器化：** Docker / Docker Compose (方便部署和环境隔离)。
*   **部署与运维：** Kubernetes (生产级可伸缩部署)，Prometheus / Grafana (监控)，ELK Stack / Grafana Loki (日志)。
*   **版本控制：** Git。

## 3. 系统架构图

以下Mermaid图展示了系统的整体架构和主要组件间的交互：

```mermaid
graph TD
    subgraph User Interface
        A[Web Frontend / CLI] --> B(Image Upload / Text Prompt)
        B --> C{View Progress & Results}
        C --> D[Model Preview / Download]
    end

    subgraph API Gateway
        E(API Endpoints)
        B -- HTTP/REST --> E
        C -- HTTP/REST --> E
    end

    subgraph Core AI Agent System
        E --> F(Orchestration Agent - Autogen)
        F -- Task Assignment --> G(Image Input & Preprocessing Module)
        G -- Preprocessed Image --> H(Image Analysis Agent - Vision LLM)
        H -- Analyzed Features --> I(Specification Generation Agent)
        I -- Spec Request --> J(Knowledge Agent - VectorDB)
        J -- API Docs / 3D Principles --> I
        I -- 3D Model Spec (JSON/YAML) --> K(Specification-to-Code Agent - Code LLM)
        K -- Code Request --> J
        J -- MCP Examples / bpy Docs --> K
        K -- Blender Python Script --> L(Blender Execution Module)
        L -- .blend / Rendered Output --> M(Output Storage)
        L -- Execution Log --> N(Validator/Debugger Agent)
        N -- Feedback / Debug Info --> F
        M -- Output Model --> O(Visual Critic Agent - Visual LLM)
        O -- Visual Feedback --> F
        F -- Task Result --> E
    end

    subgraph Data Stores
        P[Object Storage: Raw Images, .blend, Rendered Output]
        Q[Document DB: 3D Specs, Scripts, User Data]
        R[Vector DB: Knowledge Base Embeddings]
        S[Logging/Monitoring: Execution Logs]
        G -- Store Raw Image --> P
        H -- Store Analysis Result --> Q
        I -- Store 3D Spec --> Q
        K -- Store Python Script --> Q
        L -- Store .blend / Output --> P
        J -- Retrieve Embeddings --> R
        L -- Push Logs --> S
    end

    P <--> M
    Q <--> I
    Q <--> K
    R <--> J
    S <--> N

    subgraph Non-Functional Services
        T[Authentication/Authorization]
        U[Load Balancer]
        V[Monitoring & Alerting]
        W[Logging Service]
        U -- Traffic --> E
        E -- Auth Check --> T
        F -- Log Events --> W
        L -- Log Events --> W
        W --> V
    end
```

## 4. 核心模块拆分及API定义

### 4.1. 用户接口层 (User Interface Layer)

*   **Web Frontend / CLI：** 提供用户交互界面。
    *   **API：**
        *   `POST /upload_image`: 上传本地图像。
        *   `POST /generate_image`: 调用AI图像生成服务并获取图像URL。
        *   `POST /start_modeling`: 发起3D模型生成任务（输入图像ID/URL）。
        *   `GET /task_status/{task_id}`: 查询任务进度和状态。
        *   `GET /model_preview/{model_id}`: 获取模型预览图。
        *   `GET /download_model/{model_id}`: 下载生成的.blend文件或其他格式。
        *   `GET /settings`: 获取用户设置。
        *   `POST /settings`: 更新用户设置。

### 4.2. API 网关 (API Gateway)

*   **职责：** 请求路由、认证/授权、流量控制、负载均衡。
*   **技术选型：** Nginx / API Gateway Service (如AWS API Gateway)。

### 4.3. 核心AI Agent系统 (Core AI Agent System)

#### 4.3.1. 编排Agent (Orchestration Agent) - `main_orchestrator.py`

*   **职责：** 接收用户请求，协调各Agent协作，管理工作流，任务分配与状态追踪，接收并处理反馈。
*   **关键能力：** 利用AutoGen进行Agent间对话管理，利用OpenMenus-RL优化工具选择。
*   **API (内部调用)：**
    *   `orchestrate_task(image_input_data)`: 启动新的建模任务。
    *   `update_task_status(task_id, status, progress_details)`: 更新任务状态。
    *   `receive_feedback(task_id, feedback_type, details)`: 接收Validator/Visual Critic Agent的反馈。

#### 4.3.2. 图像输入与预处理模块 (Image Input & Preprocessing Module) - `input_module/image_handler.py`

*   **职责：** 处理图像加载（本地、URL、AI生成API），进行初步清理、标准化。
*   **API：**
    *   `process_image(image_data/image_url/ai_gen_params)`: 接收图像数据或AI生成参数，返回标准化后的图像路径和元数据。
    *   `call_ai_image_generation_api(prompt, style)`: 调用外部AI图像生成服务。

#### 4.3.3. 图像分析Agent (Image Analysis Agent) - `agents/image_analysis_agent.py`

*   **职责：** 利用计算机视觉和视觉LLM分析图像，提取3D模型相关特征（对象、场景、深度、材质、结构等）。
*   **API：**
    *   `analyze_image(image_path, analysis_granularity)`: 分析图像，返回结构化的图像特征数据。

#### 4.3.4. 知识Agent (Knowledge Agent) - `agents/knowledge_agent.py`

*   **职责：** 存储和检索Blender Python API (`bpy`) 文档、Molecular Nodes (MCP) 使用范例、3D建模最佳实践等专业知识。
*   **关键能力：** 向量数据库查询，上下文生成。
*   **API：**
    *   `query_knowledge(topic, keywords, context_info)`: 根据主题和关键词查询相关知识。
    *   `get_blender_api_docs(function_name)`: 获取Blender API文档片段。
    *   `get_mcp_examples(type_of_model)`: 获取MCP相关模型生成示例代码。

#### 4.3.5. 规格生成Agent (Specification Generation Agent) - `agents/spec_generation_agent.py`

*   **职责：** 接收图像分析结果，结合知识Agent的指导，生成符合预定义JSON/YAML Schema的详细3D模型规格文件。
*   **API：**
    *   `generate_spec(image_analysis_result, user_preferences)`: 生成3D模型规格，返回规格JSON/YAML。

#### 4.3.6. 规格到代码Agent (Specification-to-Code Agent) - `agents/code_generation_agent.py`

*   **职责：** 将3D模型规格转化为可执行的Blender Python脚本，特别关注MCP API调用。
*   **关键能力：** 利用LLM进行代码生成，结合知识Agent的API细节和MCP用法。
*   **API：**
    *   `generate_blender_script(model_spec_json, context_from_knowledge_agent)`: 生成Blender Python脚本，返回脚本字符串。

#### 4.3.7. Blender执行模块 (Blender Execution Module) - `blender_interface/blender_executor.py`

*   **职责：** 在Blender环境中执行生成的Python脚本，管理Blender的输出（`.blend`文件、渲染图像、视频）。
*   **关键能力：** 后台启动Blender实例（无头模式），脚本执行，结果捕获，错误日志记录。
*   **API：**
    *   `execute_script(script_string, output_path, render_settings)`: 执行Blender脚本，返回执行结果和输出文件路径。
    *   `render_model(blend_file_path, render_settings)`: 渲染Blender文件。

#### 4.3.8. Validator/Debugger Agent (可选) - `agents/validator_debugger_agent.py`

*   **职责：** 对生成的规格和代码进行验证，识别潜在错误或不一致，并提供调试反馈。
*   **API：**
    *   `validate_spec(model_spec_json)`: 验证模型规格的合法性。
    *   `debug_script(blender_script_string, execution_logs)`: 分析脚本执行日志，识别并建议修复代码问题。

#### 4.3.9. Visual Critic Agent (可选) - `agents/visual_critic_agent.py`

*   **职责：** 对Blender生成的3D模型（渲染图像或初步模型）进行视觉评估，与原始图像和期望进行对比，提供改进建议。
*   **API：**
    *   `critique_model_output(original_image, generated_render/blend_file)`: 评估模型输出，返回视觉反馈。

## 5. 数据表结构设计

### 5.1. 用户 (Users)

*   `user_id` (PK, UUID)
*   `username` (VARCHAR)
*   `email` (VARCHAR, UNIQUE)
*   `password_hash` (VARCHAR)
*   `created_at` (TIMESTAMP)
*   `last_login` (TIMESTAMP)
*   `preferences` (JSONB) - 存储用户自定义设置

### 5.2. 建模任务 (ModelingTasks)

*   `task_id` (PK, UUID)
*   `user_id` (FK, UUID)
*   `input_image_id` (FK, UUID)
*   `status` (ENUM: 'pending', 'analyzing', 'generating_spec', 'generating_code', 'executing_blender', 'validating', 'completed', 'failed')
*   `created_at` (TIMESTAMP)
*   `updated_at` (TIMESTAMP)
*   `completion_time` (TIMESTAMP, NULLABLE)
*   `error_message` (TEXT, NULLABLE)
*   `progress_details` (JSONB, NULLABLE) - 存储任务当前阶段的详细进度信息

### 5.3. 图像输入 (InputImages)

*   `image_id` (PK, UUID)
*   `user_id` (FK, UUID)
*   `storage_path` (VARCHAR) - 对象存储中的路径
*   `original_filename` (VARCHAR)
*   `format` (VARCHAR)
*   `source_type` (ENUM: 'local', 'url', 'ai_generated')
*   `ai_generation_params` (JSONB, NULLABLE) - 若AI生成，存储生成参数
*   `uploaded_at` (TIMESTAMP)

### 5.4. 图像分析结果 (ImageAnalysisResults)

*   `analysis_id` (PK, UUID)
*   `image_id` (FK, UUID)
*   `task_id` (FK, UUID)
*   `analysis_data` (JSONB) - 存储图像分析的结构化结果（对象、深度、材质等）
*   `analyzed_at` (TIMESTAMP)
*   `model_version` (VARCHAR) - 使用的图像分析模型版本

### 5.5. 模型规格 (ModelSpecifications)

*   `spec_id` (PK, UUID)
*   `task_id` (FK, UUID)
*   `spec_json` (JSONB) - 3D模型规格的JSON内容
*   `version` (INT) - 规格版本号 (用于版本控制)
*   `generated_at` (TIMESTAMP)

### 5.6. Blender脚本 (BlenderScripts)

*   `script_id` (PK, UUID)
*   `task_id` (FK, UUID)
*   `spec_id` (FK, UUID)
*   `script_content` (TEXT) - 生成的Python脚本代码
*   `version` (INT) - 脚本版本号 (用于版本控制)
*   `generated_at` (TIMESTAMP)

### 5.7. 生成模型输出 (GeneratedModelOutputs)

*   `output_id` (PK, UUID)
*   `task_id` (FK, UUID)
*   `script_id` (FK, UUID)
*   `output_type` (ENUM: '.blend', 'render_image', 'render_video')
*   `storage_path` (VARCHAR) - 对象存储中的路径
*   `generated_at` (TIMESTAMP)
*   `render_settings` (JSONB, NULLABLE)

### 5.8. 知识库条目 (KnowledgeBaseEntries)

*   `entry_id` (PK, UUID)
*   `topic` (VARCHAR)
*   `sub_topic` (VARCHAR)
*   `content` (TEXT) - 原始知识文本
*   `embedding_vector` (VECTOR) - 文本内容的向量嵌入
*   `source_url` (VARCHAR, NULLABLE)
*   `last_updated` (TIMESTAMP)

## 6. 部署方案

为了满足高可用性、可伸缩性、和性能要求，建议采用基于容器化和云原生的部署方案。

### 6.1. 开发环境

*   **Docker / Docker Compose：** 用于本地开发和测试，快速搭建所有服务（数据库、Agent容器、Blender环境）。
*   **Python Virtual Environments：** 管理项目依赖。

### 6.2. 生产环境

*   **容器化：** 所有Agent、API服务、Blender执行模块都打包成独立的Docker镜像。
*   **Kubernetes (K8s)：** 作为容器编排平台，提供自动伸缩、服务发现、负载均衡、滚动更新、故障自愈等能力。
    *   **Orchestration Agent, API Gateway, Knowledge Agent, Spec/Code Generation Agents：** 部署为常规Deployment，并配置HPA (Horizontal Pod Autoscaler) 根据CPU/内存或自定义指标进行弹性伸缩。
    *   **Image Analysis Agent, Blender Execution Module：** 部署为独立的Worker Pods，可配置GPU支持，并使用Job/CronJob或Kubernetes Queue Controller来处理任务队列。
*   **消息队列：** Kafka / RabbitMQ / Redis Streams - 用于Agent之间、模块之间异步通信和任务分发，解耦服务，提高吞吐量和可靠性。例如，图像分析任务、Blender执行任务可以放入队列。
*   **对象存储：** 云服务提供商的对象存储（AWS S3, Azure Blob Storage, GCS）或自建MinIO集群，用于存储大量图像和3D模型文件。
*   **数据库：** 部署高可用的PostgreSQL/MongoDB集群，向量数据库（如Milvus集群）。
*   **GPU资源：** 对于图像分析和Blender渲染等计算密集型任务，确保Kubernetes集群节点具备GPU资源，并配置相应的GPU调度。
*   **监控与日志：**
    *   **监控：** Prometheus + Grafana 收集各服务指标、系统资源使用情况，并进行可视化和告警。
    *   **日志：** ELK Stack (Elasticsearch, Logstash, Kibana) 或 Grafana Loki，集中收集、存储和分析所有服务的日志，便于问题排查。

### 6.3. 安全性考量

*   **VPC / Subnet隔离：** 将不同服务部署在独立的网络环境中。
*   **IAM / RBAC：** 严格的身份验证和权限管理，最小权限原则。
*   **数据加密：** 传输中加密 (TLS/SSL) 和静态加密 (数据库加密、对象存储加密)。
*   **API Key管理：** 使用Secrets Management服务（如Kubernetes Secrets, HashiCorp Vault）。
*   **DDoS防护，Web应用防火墙 (WAF)。**

## 7. 基于需求和架构设计的开发流程和步骤

以下是一个高可用的、满足需求的迭代式开发流程：

### 7.1. 阶段一：核心Agent与数据流基础 (Sprint 1-3)

1.  **环境搭建：**
    *   搭建开发环境（Docker, Python Env）。
    *   配置基本的Git仓库和CI/CD流程。
    *   初始化项目结构。
2.  **数据存储基础：**
    *   设计并实现初步的数据模型（Users, ModelingTasks, InputImages, ModelSpecifications, BlenderScripts）。
    *   选择并配置关系型/文档型数据库，完成CRUD操作。
    *   集成对象存储服务。
3.  **图像输入与预处理模块：**
    *   实现本地图像上传功能。
    *   实现图像的基本格式校验和标准化处理。
    *   定义 `InputImages` 数据模型并完成存储。
4.  **Blender执行模块基础：**
    *   搭建Blender无头模式运行环境。
    *   实现一个简单的Blender Python脚本执行器，能够执行硬编码的`.py`文件并在Blender中生成一个基础模型（如立方体）。
    *   捕获Blender执行日志和输出文件。
5.  **核心编排Agent (MVP)：**
    *   实现编排Agent的基本功能，能够接收一个简单的请求，按顺序调用图像输入和Blender执行模块。
    *   实现 `ModelingTasks` 状态的更新和追踪。

### 7.2. 阶段二：智能Agent集成与迭代 (Sprint 4-6)

1.  **图像分析Agent：**
    *   集成计算机视觉库（PyTorch/TensorFlow, OpenCV）。
    *   研究并选择合适的预训练模型进行图像特征提取（例如，目标检测模型）。
    *   实现 `analyze_image` API，能够从图像中提取基础特征数据并存储。
2.  **知识Agent：**
    *   选择并集成向量数据库（如ChromaDB）。
    *   构建初步的知识库，导入Blender API文档和少量MCP示例。
    *   实现 `query_knowledge` API。
3.  **规格生成Agent：**
    *   定义V1版本的3D模型规格JSON/YAML Schema。
    *   利用LLM（如GPT-4）实现 `generate_spec` API，将图像分析结果转化为初步的模型规格。
    *   集成知识Agent，为LLM提供上下文。
4.  **规格到代码Agent：**
    *   利用LLM（如GPT-4）实现 `generate_blender_script` API，将规格转化为Blender Python脚本。
    *   重点关注MCP插件的基础API调用。
    *   集成知识Agent，提供Blender API和MCP示例。
5.  **端到端MVP：** 实现从图像输入到Blender模型生成的完整流程（尽管模型可能简单）。

### 7.3. 阶段三：高级功能与非功能性增强 (Sprint 7-9)

1.  **AI图像生成集成：**
    *   集成DALL-E、Stable Diffusion等AI图像生成服务API。
2.  **模型质量与复杂性提升：**
    *   优化图像分析Agent，提高特征提取的粒度和准确性。
    *   细化3D模型规格Schema，支持更复杂的模型属性（材质、动画、物理）。
    *   增强规格到代码Agent，处理更复杂的Blender API和MCP功能，支持动画、物理模拟等。
3.  **Validator/Debugger Agent (可选)：**
    *   实现规格校验逻辑。
    *   基于Blender执行日志，开发脚本调试和错误识别功能。
4.  **Visual Critic Agent (可选)：**
    *   集成视觉LLM，实现对生成模型渲染图的自动评估和反馈。
5.  **版本控制：**
    *   为ModelSpecifications和BlenderScripts实现版本管理。
6.  **性能优化：**
    *   引入消息队列，异步处理耗时任务。
    *   探索GPU加速方案，优化图像分析和Blender渲染性能。
7.  **可伸缩性：**
    *   将服务容器化，准备K8s部署。
    *   初步配置K8s集群和Pod伸缩策略。
8.  **日志与监控：**
    *   集成日志系统（ELK/Loki）。
    *   配置监控仪表盘（Prometheus/Grafana）。

### 7.4. 阶段四：UI/UX与用户体验 (Sprint 10-12)

1.  **用户界面开发：**
    *   根据UI/UX设计原则，开发Web前端界面。
    *   实现图像上传、任务发起、进度查看、模型预览、下载等核心功能。
    *   注重交互的流畅性和直观性，提供实时反馈。
2.  **错误处理与用户指引：**
    *   完善前端的错误提示机制。
    *   提供操作指引、帮助文档。
3.  **个性化设置：**
    *   实现用户偏好设置的保存与加载。
4.  **集成与部署：**
    *   完成所有模块的集成测试。
    *   部署到生产环境，进行性能测试、稳定性测试。

### 7.5. 持续改进

*   **模型训练与优化：** 根据用户反馈和实际生成效果，持续优化LLM和计算机视觉模型的性能。
*   **知识库更新：** 定期更新和扩展知识Agent的知识库。
*   **功能迭代：** 根据用户需求和市场反馈，规划并开发新功能。

遵循上述流程，可以确保项目在需求清晰、架构稳健、技术先进的基础上，逐步交付高质量的Blender 3D模型生成AI Agent系统。
