# 基于图像与规格的Blender 3D模型生成AI Agent

## 项目概览

本项目旨在开发一个先进的AI Agent系统，能够根据输入的图像自动在Blender中生成复杂的3D模型。核心流程包括：
1.  **图像输入与分析**：接收用户提供的图像（无论是本地文件还是通过AI生成），并智能分析其内容以提取3D模型的关键特征和意图。
2.  **规格文件生成**：将图像分析的结果转化为结构化的3D模型规格文件（例如，JSON或YAML格式），精确描述所需模型的属性、组件和关系。
3.  **Python代码生成**：根据生成的规格文件，自动生成针对Blender的Python脚本，特别是针对Blender的Molecular Nodes (MCP)插件，以创建复杂的分子结构或其他程序化模型。
4.  **Blender内执行**：在Blender环境中执行生成的Python代码，最终输出可视化的3D模型。

本系统将集成一个专门的“知识Agent”，以提供3D UI设计原则和Blender API的专业知识，确保生成的模型既符合技术规范又具有高质量。

## 核心功能与特性

*   **全面的Agent框架支持**：采用 **AutoGen** 与 **OpenMenus-RL** 的混合 Agent 模式。AutoGen 提供强大的多 Agent 对话和协作能力，而 OpenMenus-RL 则引入了强化学习驱动的工具选择和执行优化，使得 Agent 能够更智能、更高效地利用工具和知识。
*   **集成知识类Agent**：内置一个知识库 Agent，专门存储和检索Blender的Python API (`bpy`) 文档、Molecular Nodes (MCP) 的使用范例、以及3D建模的最佳实践。这确保了生成的代码具备高正确性和效率。
*   **灵活的图像输入方式**：
    *   支持从本地文件系统上传图像作为输入。
    *   无缝集成主流AI图像生成服务（如DALL-E, Stable Diffusion等），允许用户直接使用AI生成的图像作为建模的起点。
*   **智能图像到规格转换**：利用先进的计算机视觉和深度学习技术，对输入图像进行深入分析（例如，对象识别、语义分割、深度估计），并自动推导出详细的3D模型规格。
*   **精准规格到Python代码生成**：开发一个Agent，能够将结构化的3D模型规格文件精确地转化为Blender可执行的Python代码。该Agent将特别关注Molecular Nodes (MCP) 的API调用，以支持生成复杂的、科学可视化的结构。
*   **自动化Blender模型生成**：通过Python脚本控制Blender进行模型创建、材质分配、渲染设置等操作，确保从规格到最终3D模型输出的端到端自动化。特别关注在Blender的MCP环境中执行代码，以生成高质量的模型。

## 技术栈与主要模块

*   **编程语言**: Python
*   **AI Agent框架**: Autogen (for multi-agent orchestration and conversation), OpenMenus-RL (for RL-driven tool/menu selection and action optimization within agents).
*   **大型语言模型 (LLMs)**: OpenAI API (GPT系列), 也可以考虑集成本地开源LLM (如Llama 2) 用于代码生成和图像理解。
*   **计算机视觉**: OpenCV, PyTorch/TensorFlow，并可能使用预训练模型（如DETR, Mask R-CNN）进行图像分析。
*   **Blender集成**: `bpy` (Blender官方Python API), Molecular Nodes (MCP) 插件。
*   **数据存储**: 向量数据库 (如ChromaDB, FAISS) 用于知识Agent的知识检索。
*   **规格格式**: JSON / YAML
*   **版本控制**: Git

## 架构概览

本系统的核心在于多个专业Agent之间的协作与通信，由一个顶层编排Agent进行管理，并利用OpenMenus-RL增强其决策能力。

```
[用户输入: 图像 (本地/AI生成)]
        |\
        V \ (图像数据)
[1. 图像输入与预处理模块]
        |
        V
[2. 图像分析Agent (Vision-enhanced)] --(提取特征/意图)-->
        |
        V
[3. 规格生成Agent] <---------> [知识Agent (3D UI/Blender API/MCP)]
        | ^ (知识查询/上下文)
        | (结构化3D规格文件)
        V
[4. 规格到代码Agent (LLM-powered)] --(Blender Python脚本)-->
        |
        V
[5. Blender执行模块 (通过Python API)] --(在Blender MCP中执行)-->
        |
        V
[6. 3D模型输出 (Blender .blend / Render)]

------------------------------------------------------------------------------------------------------
|                                                                                                    |
|                                [Autogen + OpenMenus-RL 混合Agent框架]                             |
|                                     (负责Agent间通信, 任务分配, 工具选择优化)                      |
|                                                                                                    |
------------------------------------------------------------------------------------------------------
```

**Agent职责划分：**

*   **编排Agent (Orchestration Agent)**: 系统的大脑，负责管理整个工作流，协调各个Agent之间的交互，接收用户请求，并最终将任务结果返回。将利用 Autogen 的对话能力和 OpenMenus-RL 的决策机制。
*   **图像输入与预处理模块 (Image Input & Preprocessing Module)**: 处理图像的加载（本地或通过API获取），并进行初步的清理和标准化，为后续分析做准备。
*   **图像分析Agent (Image Analysis Agent)**: 专门处理计算机视觉任务，识别图像中的对象、结构、材质、空间关系等，并将其转化为对3D模型有意义的初步语义信息。
*   **知识Agent (Knowledge Agent)**: 作为系统的专家知识库，提供Blender Python API、MCP特定功能以及通用3D建模原理的上下文信息。该Agent能够回答其他Agent关于Blender操作、参数或最佳实践的查询。
*   **规格生成Agent (Specification Generation Agent)**: 接收图像分析结果，结合知识Agent的指导，生成符合预定义JSON/YAML Schema的详细3D模型规格文件。
*   **规格到代码Agent (Specification-to-Code Agent)**: 核心代码生成器，将详细的3D模型规格转化为可执行的Blender Python脚本。该Agent将充分利用LLM的能力，并结合知识Agent提供的API细节和MCP用法，确保生成代码的正确性、鲁棒性和Blender兼容性。
*   **Blender执行模块 (Blender Execution Module)**: 负责在后台启动Blender实例（无头模式），加载并执行生成的Python脚本，管理Blender的输出（如保存`.blend`文件，渲染图像等）。

## 开发计划 (参考 `ASC.md` 文件获取详细的任务列表和时间线)

本项目将分为多个阶段进行，从基础研究到核心功能开发，再到高级特性和优化，确保每一步都稳健推进。具体任务和交付物请参阅 `ASC.md` 文件。

## 如何运行 (开发中)

待项目核心模块完成后，此处将提供详细的设置和运行指南。通常会涉及：

1.  安装Python依赖。
2.  配置Blender路径和MCP插件。
3.  设置必要的API密钥（如OpenAI API Key）。
4.  运行主编排脚本。
