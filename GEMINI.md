# Gemini Workspace

This file provides context for the AI assistant to understand the project structure and conventions.

## Project Overview

This project appears to be a Python-based application for creating or manipulating 3D models, possibly using Blender. It includes agents for orchestration, a Blender interface, and documentation.

## Key Files

*   `pyproject.toml`: Defines project metadata and dependencies.
*   `requirements.txt`: Lists Python dependencies.
*   `environment.yml`: Likely for Conda environment setup.
*   `agents/orchestration_agent.py`: The main agent for coordinating tasks.
*   `blender_interface/`: Code for interacting with Blender.
*   `docs/`: Contains project documentation.

## Development Workflow

1.  **Dependencies:** Install dependencies using `pip install -r requirements.txt` or `conda env create -f environment.yml`.
2.  **Testing:** Run tests using `pytest`.
3.  **Documentation:** Keep documentation in the `docs/` directory up-to-date.

## Conventions

*   Use the `agents` directory for all AI agent code.
*   Place Blender-related code in the `blender_interface` directory.
*   Follow standard Python coding conventions (PEP 8).
