import os
import subprocess
import sys
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv(dotenv_path=os.path.join(os.path.dirname(__file__), '../.env'))

def test_blender_headless_creation(output_blend_path="test_cube.blend"):
    """
    Tests if Blender can be run in headless mode to create a cube and save it.

    Args:
        output_blend_path (str): The desired output path for the .blend file.
    """
    blender_path = os.getenv("BLENDER_PATH")

    if not blender_path:
        print("Error: BLENDER_PATH environment variable is not set in .env file.")
        print("Please update models/.env with the correct path to your Blender executable.")
        sys.exit(1)

    if not os.path.exists(blender_path):
        print(f"Error: Blender executable not found at '{blender_path}'.")
        print("Please ensure the BLENDER_PATH in models/.env is correct and the executable exists.")
        sys.exit(1)

    print(f"Using Blender executable: {blender_path}")
    print(f"Attempting to create and save a cube to: {os.path.abspath(output_blend_path)}")

    # Define a simple Python script that Blender will execute
    # This script will create a cube and save the .blend file
    blender_script_content = f"""
import bpy
import os

# Delete all default objects (camera, light, cube)
bpy.ops.wm.read_factory_settings(use_empty=True)

# Create a new cube at the origin
bpy.ops.mesh.primitive_cube_add(size=2.0, enter_editmode=False, align='WORLD', location=(0, 0, 0))

# Get the absolute path for saving
save_path = os.path.abspath(r\'{output_blend_path}\')

# Ensure the output directory exists
output_dir = os.path.dirname(save_path)
if output_dir and not os.path.exists(output_dir):
    os.makedirs(output_dir)

# Save the .blend file
bpy.ops.wm.save_as_mainfile(filepath=save_path)
print(f"Successfully saved Blender file to {{save_path}}")
    """

    # Save the Blender script to a temporary file
    script_file_path = "temp_blender_script.py"
    try:
        with open(script_file_path, "w") as f:
            f.write(blender_script_content)

        # Construct the command to run Blender in headless mode and execute the script
        command = [
            blender_path,
            "--background",  # Run Blender in background (headless)
            "--python", script_file_path # Execute the Python script
        ]

        # Execute the Blender command
        print(f"Running command: {' '.join(command)}")
        result = subprocess.run(command, capture_output=True, text=True, check=True)

        print("Blender output:")
        print(result.stdout)
        if result.stderr:
            print("Blender errors:")
            print(result.stderr)

        if os.path.exists(output_blend_path):
            print(f"Success: Blender file '{output_blend_path}' created successfully!")
        else:
            print(f"Failure: Blender file '{output_blend_path}' was not created.")
            sys.exit(1)

    except subprocess.CalledProcessError as e:
        print(f"Error running Blender: {e}")
        print("STDOUT:")
        print(e.stdout)
        print("STDERR:")
        print(e.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        sys.exit(1)
    finally:
        # Clean up the temporary script file
        if os.path.exists(script_file_path):
            os.remove(script_file_path)

if __name__ == "__main__":
    # Ensure we are in the models directory or handle output path relative to it
    current_dir = os.path.dirname(os.path.abspath(__file__))
    output_path = os.path.join(current_dir, "output", "test_cube.blend")

    # Create the output directory if it doesn't exist
    output_dir = os.path.dirname(output_path)
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    test_blender_headless_creation(output_path)
